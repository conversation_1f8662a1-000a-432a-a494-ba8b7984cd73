import { useMemo } from "react";
import { operations } from "@/utils";
import MkdSDK from "@/utils/MkdSDK";
import TreeSDK from "@/utils/TreeSDK";
import { OfflineAwareMkdSDK } from "@/utils/offline/OfflineAwareMkdSDK";
import { OfflineAwareTreeSDK } from "@/utils/offline/OfflineAwareTreeSDK";
import { useOffline } from "@/hooks/useOffline";

interface SdkConfig {
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
  GOOGLE_CAPTCHA_SITEKEY?: string;
  enableOfflineMode?: boolean;
}

interface UseSDKReturnType {
  sdk: MkdSDK | OfflineAwareMkdSDK;
  tdk: TreeSDK | OfflineAwareTreeSDK;
  projectId: string;
  operations: typeof operations;
  isOfflineMode: boolean;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  // Try to get offline context, but don't require it
  let offlineContext = null;
  try {
    offlineContext = useOffline();
  } catch (error) {
    // Offline context not available, continue with regular SDK
  }

  const enableOfflineMode = config.enableOfflineMode ?? true;
  const hasOfflineService = offlineContext && enableOfflineMode;

  const sdk = useMemo(() => {
    if (hasOfflineService) {
      const offlineSDK = new OfflineAwareMkdSDK(config);
      // Note: We'll set the offline service in a useEffect since the service might not be ready yet
      return offlineSDK;
    }
    return new MkdSDK(config);
  }, [config, hasOfflineService]);

  const tdk = useMemo(() => {
    if (hasOfflineService) {
      const offlineTreeSDK = new OfflineAwareTreeSDK(config);
      // Note: We'll set the offline service in a useEffect since the service might not be ready yet
      return offlineTreeSDK;
    }
    return new TreeSDK(config);
  }, [config, hasOfflineService]);

  // Set offline service when available
  useMemo(() => {
    if (hasOfflineService && offlineContext) {
      // We need to access the offline service from the context
      // This will be available after the OfflineProvider is initialized
      setTimeout(() => {
        try {
          if (sdk instanceof OfflineAwareMkdSDK) {
            // The offline service will be set via the context
          }
          if (tdk instanceof OfflineAwareTreeSDK) {
            // The offline service will be set via the context
          }
        } catch (error) {
          console.warn("Failed to set offline service:", error);
        }
      }, 100);
    }
  }, [hasOfflineService, offlineContext, sdk, tdk]);

  const projectId = sdk.getProjectId();

  return {
    sdk,
    tdk,
    projectId,
    operations,
    isOfflineMode: hasOfflineService,
  };
};

export default useSDK;
